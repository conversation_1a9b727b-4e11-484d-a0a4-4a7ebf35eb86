import {
  Controller,
  Get,
  Post,
  Put,
  Del,
  Inject,
  Param,
  Body,
  Query,
} from '@midwayjs/core';
import { CouponOrderService } from '../service/coupon-order.service';
import { CouponOrderStatus } from '../entity/coupon-order.entity';
import { CustomError } from '../error/custom.error';

@Controller('/coupon-orders')
export class CouponOrderController {
  @Inject()
  service: CouponOrderService;

  @Get('/all', { summary: '查询所有代金券订单列表' })
  async findAll(
    @Query('status') status: string,
    @Query('current') current: number,
    @Query('pageSize') pageSize: number
  ) {
    const query: any = {};
    if (status) {
      query.status = status.split(',');
    }

    return await this.service.findAll({
      query,
      offset: (current - 1) * pageSize,
      limit: pageSize,
      order: [['updatedAt', 'DESC']],
      include: ['customer', 'coupon'],
    });
  }

  @Get('/', { summary: '查询代金券订单列表' })
  async index(
    @Query('customerId') customerId: number,
    @Query('status') status: string,
    @Query('current') current: number,
    @Query('pageSize') pageSize: number
  ) {
    if (!customerId) {
      throw new CustomError('用户ID不能为空');
    }

    let statusArray: CouponOrderStatus[] = [];
    if (status) {
      statusArray = status.split(',') as CouponOrderStatus[];
    }

    return await this.service.findCustomerOrders(
      customerId,
      statusArray,
      current,
      pageSize
    );
  }

  @Get('/:id', { summary: '按ID查询代金券订单' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定代金券订单');
    }
    return res;
  }

  @Post('/', { summary: '创建代金券订单' })
  async create(
    @Body()
    body: {
      customerId: number;
      couponId: number;
      remark?: string;
    }
  ) {
    const { customerId, couponId, remark } = body;
    if (!customerId) {
      throw new CustomError('用户ID不能为空');
    }
    if (!couponId) {
      throw new CustomError('代金券ID不能为空');
    }

    return await this.service.createOrder(customerId, couponId, remark);
  }

  @Post('/:sn/pay', { summary: '支付代金券订单' })
  async pay(@Param('sn') sn: string, @Body('customerId') customerId: number) {
    if (!customerId) {
      throw new CustomError('用户ID不能为空');
    }

    return await this.service.payOrder(customerId, sn);
  }

  @Put('/:sn/cancel', { summary: '取消代金券订单' })
  async cancel(
    @Param('sn') sn: string,
    @Body('customerId') customerId: number
  ) {
    if (!customerId) {
      throw new CustomError('用户ID不能为空');
    }

    return await this.service.cancelOrder(customerId, sn);
  }

  @Del('/:id', { summary: '删除代金券订单' })
  async delete(
    @Param('id') id: number,
    @Body('customerId') customerId: number
  ) {
    if (!customerId) {
      throw new CustomError('用户ID不能为空');
    }

    return await this.service.deleteOrder(customerId, id);
  }
}
