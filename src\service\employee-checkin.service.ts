import { Provide } from '@midwayjs/core';
import {
  EmployeeCheckIn,
  CheckInPhotos,
} from '../entity/employee-checkin.entity';
import { Employee } from '../entity/employee.entity';
import { BaseService } from '../common/BaseService';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';

@Provide()
export class EmployeeCheckInService extends BaseService<EmployeeCheckIn> {
  constructor() {
    super('员工打卡记录');
  }

  getModel() {
    return EmployeeCheckIn;
  }

  /**
   * 验证分组照片
   */
  private validateGroupedPhotos(photos: CheckInPhotos) {
    const groups = [
      'vehicleExterior',
      'serviceStaff',
      'vehicleInterior',
    ] as const;
    const groupNames = {
      vehicleExterior: '车辆外观',
      serviceStaff: '服务人员',
      vehicleInterior: '车内情况',
    };

    let totalPhotos = 0;
    let hasPhotos = false;

    for (const group of groups) {
      const groupPhotos = photos[group] || [];
      if (groupPhotos.length > 0) {
        hasPhotos = true;
        if (groupPhotos.length > 9) {
          throw new CustomError(`${groupNames[group]}照片最多只能上传9张`, 400);
        }
        totalPhotos += groupPhotos.length;
      }
    }

    if (!hasPhotos) {
      throw new CustomError('至少需要上传一张照片', 400);
    }

    return totalPhotos;
  }

  /**
   * 员工打卡
   */
  async checkIn(data: {
    employeeId: number;
    photos: CheckInPhotos;
    description?: string;
    address?: string;
    longitude?: number;
    latitude?: number;
  }) {
    // 验证照片分组
    this.validateGroupedPhotos(data.photos);

    // 验证员工是否存在且在职
    const employee = await Employee.findByPk(data.employeeId);
    if (!employee) {
      throw new CustomError('员工不存在', 404);
    }

    // 检查员工是否已离职
    if (employee.status === 0) {
      throw new CustomError('员工已离职，无法进行打卡操作', 403);
    }

    // 创建打卡记录
    const checkIn = await this.create({
      employeeId: data.employeeId,
      photos: data.photos,
      description: data.description,
      address: data.address,
      longitude: data.longitude,
      latitude: data.latitude,
      checkInTime: new Date(),
    });

    return await this.findOne({
      where: { id: checkIn.id },
      include: [Employee],
    });
  }

  /**
   * 查询员工的打卡记录列表
   */
  async findByEmployeeId(
    employeeId: number,
    current = 1,
    pageSize = 10,
    startDate?: string,
    endDate?: string
  ) {
    const offset = (current - 1) * pageSize;
    const limit = pageSize;

    // 构建查询条件
    const whereCondition: any = { employeeId };

    // 日期筛选
    if (startDate || endDate) {
      whereCondition.checkInTime = {};
      if (startDate) {
        whereCondition.checkInTime[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereCondition.checkInTime[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    return await this.findAll({
      query: whereCondition,
      offset,
      limit,
      include: [Employee],
      order: [['checkInTime', 'DESC']],
    });
  }

  /**
   * 管理端查询所有员工打卡记录
   */
  async findAllCheckIns(
    current = 1,
    pageSize = 10,
    employeeId?: number,
    startDate?: string,
    endDate?: string,
    keyword?: string
  ) {
    const offset = (current - 1) * pageSize;
    const limit = pageSize;

    // 构建查询条件
    const whereCondition: any = {};

    if (employeeId) {
      whereCondition.employeeId = employeeId;
    }

    // 日期筛选
    if (startDate || endDate) {
      whereCondition.checkInTime = {};
      if (startDate) {
        whereCondition.checkInTime[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereCondition.checkInTime[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    // 员工信息筛选
    const includeCondition: any = {
      model: Employee,
      required: true,
    };

    if (keyword) {
      includeCondition.where = {
        [Op.or]: [
          { name: { [Op.like]: `%${keyword}%` } },
          { phone: { [Op.like]: `%${keyword}%` } },
        ],
      };
    }

    return await this.findAll({
      query: whereCondition,
      offset,
      limit,
      include: [includeCondition],
      order: [['checkInTime', 'DESC']],
    });
  }

  /**
   * 获取打卡统计信息
   */
  async getStatistics(
    employeeId?: number,
    startDate?: string,
    endDate?: string
  ) {
    // 构建查询条件
    const whereCondition: any = {};

    if (employeeId) {
      whereCondition.employeeId = employeeId;
    }

    // 日期筛选
    if (startDate || endDate) {
      whereCondition.checkInTime = {};
      if (startDate) {
        whereCondition.checkInTime[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereCondition.checkInTime[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    // 总打卡次数
    const totalCount = await EmployeeCheckIn.count({
      where: whereCondition,
    });

    // 今日打卡次数
    const today = new Date();
    const todayStart = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000 - 1);

    const todayCondition = { ...whereCondition };
    todayCondition.checkInTime = {
      [Op.gte]: todayStart,
      [Op.lte]: todayEnd,
    };

    const todayCount = await EmployeeCheckIn.count({
      where: todayCondition,
    });

    // 参与打卡的员工数量
    const employeeCount = await EmployeeCheckIn.count({
      where: whereCondition,
      distinct: true,
      col: 'employeeId',
    });

    return {
      totalCount,
      todayCount,
      employeeCount,
    };
  }

  /**
   * 查询员工最后一次打卡时间
   */
  async getLastCheckInTime(employeeId: number) {
    // 验证员工是否存在
    const employee = await Employee.findByPk(employeeId);
    if (!employee) {
      throw new CustomError('员工不存在', 404);
    }

    // 查询最后一次打卡记录
    const lastCheckIn = await EmployeeCheckIn.findOne({
      where: { employeeId },
      order: [['checkInTime', 'DESC']],
      attributes: ['id', 'checkInTime'],
    });

    // 查询总打卡次数
    const totalCount = await EmployeeCheckIn.count({
      where: { employeeId },
    });

    // 查询今日打卡次数
    const today = new Date();
    const todayStart = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000 - 1);

    const todayCount = await EmployeeCheckIn.count({
      where: {
        employeeId,
        checkInTime: {
          [Op.gte]: todayStart,
          [Op.lte]: todayEnd,
        },
      },
    });

    return {
      employeeId,
      lastCheckInTime: lastCheckIn?.checkInTime || null,
      lastCheckInId: lastCheckIn?.id || null,
      totalCheckInCount: totalCount,
      todayCheckInCount: todayCount,
    };
  }

  /**
   * 删除打卡记录
   */
  async deleteCheckIn(id: number, employeeId?: number) {
    const checkIn = await this.findById(id);
    if (!checkIn) {
      throw new CustomError('打卡记录不存在', 404);
    }

    // 如果指定了员工ID，验证是否为该员工的记录
    if (employeeId && checkIn.employeeId !== employeeId) {
      throw new CustomError('无权删除此打卡记录', 403);
    }

    await this.delete({ id });
    return true;
  }
}
